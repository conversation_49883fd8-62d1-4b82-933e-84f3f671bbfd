2025-07-23 00:08:06,872 - __main__ - INFO - Starting pagination fix tests...
2025-07-23 00:08:06,873 - __main__ - INFO - === Testing Pagination Calculations ===
2025-07-23 00:08:06,874 - __main__ - INFO - Testing comment number to page calculations:
2025-07-23 00:08:06,887 - __main__ - INFO - 
Testing total comments to last page calculations:
2025-07-23 00:08:06,893 - __main__ - INFO - 
=== Testing Incremental Strategy Selection ===
2025-07-23 00:08:06,893 - __main__ - INFO - 
Small increment (6 comments) - should use last page only
2025-07-23 00:08:06,893 - __main__ - INFO -   Comments: 699 -> 705 (+6)
2025-07-23 00:08:06,894 - __main__ - INFO -   Pages: 24 -> 24 (calculated)
2025-07-23 00:08:06,894 - __main__ - INFO -   Strategy: last_page_only
2025-07-23 00:08:06,894 - __main__ - INFO -   Actual pages to process: 24 -> 24 (1 pages)
2025-07-23 00:08:06,894 - __main__ - INFO - 
Medium increment (21 comments) - should use last page only
2025-07-23 00:08:06,894 - __main__ - INFO -   Comments: 699 -> 720 (+21)
2025-07-23 00:08:06,894 - __main__ - INFO -   Pages: 24 -> 24 (calculated)
2025-07-23 00:08:06,894 - __main__ - INFO -   Strategy: last_page_only
2025-07-23 00:08:06,894 - __main__ - INFO -   Actual pages to process: 24 -> 24 (1 pages)
2025-07-23 00:08:06,894 - __main__ - INFO - 
Large increment (31 comments) - should use multiple pages
2025-07-23 00:08:06,895 - __main__ - INFO -   Comments: 699 -> 730 (+31)
2025-07-23 00:08:06,895 - __main__ - INFO -   Pages: 24 -> 25 (calculated)
2025-07-23 00:08:06,895 - __main__ - INFO -   Strategy: full_multi_page
2025-07-23 00:08:06,895 - __main__ - INFO -   Actual pages to process: 24 -> 25 (2 pages)
2025-07-23 00:08:06,895 - __main__ - INFO - 
Large increment (53 comments) - should use multiple pages
2025-07-23 00:08:06,895 - __main__ - INFO -   Comments: 699 -> 752 (+53)
2025-07-23 00:08:06,895 - __main__ - INFO -   Pages: 24 -> 26 (calculated)
2025-07-23 00:08:06,895 - __main__ - INFO -   Strategy: full_multi_page
2025-07-23 00:08:06,895 - __main__ - INFO -   Actual pages to process: 24 -> 26 (3 pages)
2025-07-23 00:08:06,895 - __main__ - INFO - 
Very large increment (200 comments) - should limit pages
2025-07-23 00:08:06,895 - __main__ - INFO -   Comments: 600 -> 800 (+200)
2025-07-23 00:08:06,896 - __main__ - INFO -   Pages: 21 -> 27 (calculated)
2025-07-23 00:08:06,896 - __main__ - INFO -   Strategy: limited_multi_page
2025-07-23 00:08:06,896 - __main__ - INFO -   Actual pages to process: 23 -> 27 (5 pages)
2025-07-23 00:08:06,896 - __main__ - INFO - 
Massive increment (400 comments) - should limit pages
2025-07-23 00:08:06,896 - __main__ - INFO -   Comments: 100 -> 500 (+400)
2025-07-23 00:08:06,896 - __main__ - INFO -   Pages: 4 -> 17 (calculated)
2025-07-23 00:08:06,896 - __main__ - INFO -   Strategy: limited_multi_page
2025-07-23 00:08:06,896 - __main__ - INFO -   Actual pages to process: 13 -> 17 (5 pages)
2025-07-23 00:08:06,896 - __main__ - INFO - 
=== Testing URL Building ===
2025-07-23 00:08:06,896 - __main__ - INFO - 
Testing URL: https://lowendtalk.com/discussion/207593/hostodo-let-exclusive-8gb-4c-nvme-for-6-16gb-6c-for-10-epyc-power-instant-deploy
2025-07-23 00:08:06,896 - __main__ - INFO -   Page 1: https://lowendtalk.com/discussion/207593/hostodo-let-exclusive-8gb-4c-nvme-for-6-16gb-6c-for-10-epyc-power-instant-deploy
2025-07-23 00:08:06,896 - __main__ - INFO -   Page 2: https://lowendtalk.com/discussion/207593/hostodo-let-exclusive-8gb-4c-nvme-for-6-16gb-6c-for-10-epyc-power-instant-deploy/p2
2025-07-23 00:08:06,897 - __main__ - INFO -   Page 3: https://lowendtalk.com/discussion/207593/hostodo-let-exclusive-8gb-4c-nvme-for-6-16gb-6c-for-10-epyc-power-instant-deploy/p3
2025-07-23 00:08:06,897 - __main__ - INFO -   Page 24: https://lowendtalk.com/discussion/207593/hostodo-let-exclusive-8gb-4c-nvme-for-6-16gb-6c-for-10-epyc-power-instant-deploy/p24
2025-07-23 00:08:06,897 - __main__ - INFO -   Page 26: https://lowendtalk.com/discussion/207593/hostodo-let-exclusive-8gb-4c-nvme-for-6-16gb-6c-for-10-epyc-power-instant-deploy/p26
2025-07-23 00:08:06,897 - __main__ - INFO - 
Testing URL: https://lowendtalk.com/discussion/207592/data-brokers-are-scraping-then-selling-your-data-cbp-and-ice-buy-it-all-up
2025-07-23 00:08:06,897 - __main__ - INFO -   Page 1: https://lowendtalk.com/discussion/207592/data-brokers-are-scraping-then-selling-your-data-cbp-and-ice-buy-it-all-up
2025-07-23 00:08:06,897 - __main__ - INFO -   Page 2: https://lowendtalk.com/discussion/207592/data-brokers-are-scraping-then-selling-your-data-cbp-and-ice-buy-it-all-up/p2
2025-07-23 00:08:06,897 - __main__ - INFO -   Page 3: https://lowendtalk.com/discussion/207592/data-brokers-are-scraping-then-selling-your-data-cbp-and-ice-buy-it-all-up/p3
2025-07-23 00:08:06,897 - __main__ - INFO -   Page 24: https://lowendtalk.com/discussion/207592/data-brokers-are-scraping-then-selling-your-data-cbp-and-ice-buy-it-all-up/p24
2025-07-23 00:08:06,897 - __main__ - INFO -   Page 26: https://lowendtalk.com/discussion/207592/data-brokers-are-scraping-then-selling-your-data-cbp-and-ice-buy-it-all-up/p26
2025-07-23 00:08:06,897 - __main__ - INFO - 
=== All Tests Completed ===
