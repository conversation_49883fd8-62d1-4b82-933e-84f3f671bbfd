 <!DOCTYPE html>
		<html
			class="layout layout-sidecar-right    question  default-focus"
			lang="zh-cn"
			dir="ltr"
			data-authenticated="false"
			data-auth-status-determined="false"
			data-target="docs"
			x-ms-format-detection="none"
		>
			
		<head>
			<title>windows10-64系统调节亮度选项消失，无法调节亮度 - Microsoft Q&amp;A</title>
			<meta charset="utf-8" />
			<meta name="viewport" content="width=device-width, initial-scale=1.0" />
			<meta name="color-scheme" content="light dark" />

			<meta name="description" content="操作体统:win10-64家庭版 
版本号：1903
调节亮度选项消失时间大概在7月中旬
     以前一直没出过问题，出问题前曾更新过驱动，在安装的时候屏幕卡屏了一下，后来发现没有‘’调节亮度‘’这个选项了，右键电源，电源选项中的下方，都没有这个选项了，亮度也无法调节，电源选项的小太阳图标灰色，无法调节，windows 移动中心的那个亮度调节可以拖动，但是屏幕亮度没有变化。
    在网上百度过，改注册表，重新装驱动，都没有用，后来用windows10自带的重置电脑，也没有用。" />
			<link rel="canonical" href="https://learn.microsoft.com/zh-cn/answers/questions/3209892/windows10-64" /> 

			<!-- Non-customizable open graph and sharing-related metadata -->
			<meta name="twitter:card" content="summary_large_image" />
			<meta name="twitter:site" content="@MicrosoftLearn" />
			<meta property="og:type" content="website" />
			<meta property="og:image:alt" content="Microsoft Learn" />
			<meta property="og:image" content="https://learn.microsoft.com/en-us/media/open-graph-image.png" />
			<!-- Page specific open graph and sharing-related metadata -->
			<meta property="og:title" content="windows10-64系统调节亮度选项消失，无法调节亮度 - Microsoft Q&amp;A" />
			<meta property="og:url" content="https://learn.microsoft.com/zh-cn/answers/questions/3209892/windows10-64" />
			<meta property="og:description" content="操作体统:win10-64家庭版 
版本号：1903
调节亮度选项消失时间大概在7月中旬
     以前一直没出过问题，出问题前曾更新过驱动，在安装的时候屏幕卡屏了一下，后来发现没有‘’调节亮度‘’这个选项了，右键电源，电源选项中的下方，都没有这个选项了，亮度也无法调节，电源选项的小太阳图标灰色，无法调节，windows 移动中心的那个亮度调节可以拖动，但是屏幕亮度没有变化。
    在网上百度过，改注册表，重新装驱动，都没有用，后来用windows10自带的重置电脑，也没有用。" />
			 
			<meta name="locale" content="zh-cn" />
			 
			<meta name="uhfHeaderId" content="answersv2" />

			<meta name="page_type" content="QnA" />

			<!--page specific meta tags-->
			<meta name="robots" content="all" />

			<!-- custom meta tags -->
			
		<meta name="authorId" content="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf" />
	
		<meta name="createdAt" content="2019-08-29T08:06:09+00:00" />
	
		<meta name="updatedAt" content="2025-07-08T14:48:02.1+00:00" />
	
		<meta name="pageCount" content="2" />
	
		<meta name="tag" content="https://microsoft-devrel.poolparty.biz/QnACompound/db066425-6642-4ab6-8ab8-46b411640dbb" />
	
		<meta name="questionId" content="3209892" />
	
		<meta name="search.isTopContent" content="false" />
	 

			<!-- assets and js globals -->
			<link rel="stylesheet" href="/static/assets/0.4.03111.6953-696b2140/styles/site-ltr.css" />
			
			<script src="https://wcpstatic.microsoft.com/mscc/lib/v2/wcp-consent.js"></script>
			<script src="https://js.monitor.azure.com/scripts/c/ms.jsll-4.min.js"></script>
			<script src="/_themes/docs.theme/master/zh-cn/_themes/global/deprecation.js"></script>

			<!-- msdocs global object -->
			<script id="msdocs-script">
		var msDocs = {
  "environment": {
    "accessLevel": "online",
    "azurePortalHostname": "portal.azure.com",
    "reviewFeatures": false,
    "supportLevel": "production",
    "systemContent": true,
    "siteName": "learn",
    "legacyHosting": false
  },
  "data": {
    "contentLocale": "zh-cn",
    "contentDir": "ltr",
    "userLocale": "zh-cn",
    "userDir": "ltr",
    "pageTemplate": "Question",
    "brand": "",
    "context": {},
    "standardFeedback": false,
    "showFeedbackReport": false,
    "feedbackHelpLinkType": "",
    "feedbackHelpLinkUrl": "",
    "feedbackSystem": "",
    "feedbackGitHubRepo": "",
    "feedbackProductUrl": "",
    "extendBreadcrumb": false,
    "isEditDisplayable": false,
    "isPrivateUnauthorized": false,
    "hideViewSource": false,
    "isPermissioned": false,
    "hasRecommendations": true,
    "contributors": []
  },
  "functions": {}
};;
	</script>

			<!-- base scripts, msdocs global should be before this -->
			
				<script src="/static/assets/0.4.03111.6953-696b2140/scripts/zh-cn/index-critical-qna.js"></script>
				<script defer src="/static/assets/0.4.03111.6953-696b2140/scripts/zh-cn/index-qna.js"></script>
			
			

			<!-- json-ld -->
			<script type="application/ld+json">
				{"@context":"https://schema.org","@type":"QAPage","mainEntity":{"@type":"Question","id":3209892,"name":"windows10-64系统调节亮度选项消失，无法调节亮度","text":"\u003cp\u003e操作体统:win10-64家庭版 \u003c/p\u003e\n\u003cp\u003e版本号：1903\u003c/p\u003e\n\u003cp\u003e调节亮度选项消失时间大概在7月中旬\u003c/p\u003e\n\u003cp\u003e     以前一直没出过问题，出问题前曾更新过驱动，在安装的时候屏幕卡屏了一下，后来发现没有‘’调节亮度‘’这个选项了，右键电源，电源选项中的下方，都没有这个选项了，亮度也无法调节，电源选项的小太阳图标灰色，无法调节，windows 移动中心的那个亮度调节可以拖动，但是屏幕亮度没有变化。\u003c/p\u003e\n\u003cp\u003e    在网上百度过，改注册表，重新装驱动，都没有用，后来用windows10自带的重置电脑，也没有用。\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://filestore.community.support.microsoft.com/api/images/f4efc3b0-7e0d-48eb-9b22-7ad72f884d58?upload=true\" rel=\"ugc nofollow\"\u003e\u003cimg src=\"https://filestore.community.support.microsoft.com/api/images/f4efc3b0-7e0d-48eb-9b22-7ad72f884d58?upload=true\" alt=\"\" /\u003e\u003c/a\u003e\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://filestore.community.support.microsoft.com/api/images/f15ce0bd-3be1-4770-8dc2-28b32379b4c5?upload=true\" rel=\"ugc nofollow\"\u003e\u003cimg src=\"https://filestore.community.support.microsoft.com/api/images/f15ce0bd-3be1-4770-8dc2-28b32379b4c5?upload=true\" alt=\"\" /\u003e\u003c/a\u003e\u003ca href=\"https://filestore.community.support.microsoft.com/api/images/b94799c9-2ad5-47c8-b7fb-8caa63040928?upload=true\" rel=\"ugc nofollow\"\u003e\u003cimg src=\"https://filestore.community.support.microsoft.com/api/images/b94799c9-2ad5-47c8-b7fb-8caa63040928?upload=true\" alt=\"\" /\u003e\u003c/a\u003e\u003c/p\u003e","answerCount":9,"upvoteCount":96,"author":"","authorId":"a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf","acceptedAnswer":[],"suggestedAnswer":[{"@type":"Answer","id":5042357,"text":"\u003cp\u003e您好,我是独立顾问(Independent Advisor)Dexter,请让我来帮助您.\u003c/p\u003e\n\u003cp\u003e请您先右键点击开始按钮\u0026gt;运行\u0026gt;输入winver回车查看一下系统版本的具体信息,可以截图回复一下\u003c/p\u003e\n","upvoteCount":4,"author":"","authorId":"a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf","url":"https://learn.microsoft.com/zh-cn/answers/a/5042357"},{"@type":"Answer","id":5042873,"text":"\u003cp\u003e......     \u003ca href=\"https://filestore.community.support.microsoft.com/api/images/50853dc4-7247-4c21-a9a8-23ad2559413f?upload=true\" rel=\"ugc nofollow\"\u003e\u003cimg src=\"https://filestore.community.support.microsoft.com/api/images/50853dc4-7247-4c21-a9a8-23ad2559413f?upload=true\" alt=\"\" /\u003e\u003c/a\u003e\u003c/p\u003e\n","upvoteCount":3,"author":"","authorId":"a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf","url":"https://learn.microsoft.com/zh-cn/answers/a/5042873"},{"@type":"Answer","id":5042368,"text":"\u003cp\u003e嗯,已经更新到最新的版本了,建议您可以参考下面的步骤对系统镜像进行校验和修复试试:\u003c/p\u003e\n\u003col\u003e\n\u003cli\u003e 按键盘Win+R打开\u0026quot;运行\u0026quot;,输入cmd然后按Ctrl+Shift+回车,以管理员身份打开命令提示符\u003c/li\u003e\n\u003cli\u003e 在打开的命令提示符窗口中粘贴运行下面的命令:\u003c/li\u003e\n\u003c/ol\u003e\n\u003cp\u003eDISM.exe /Online /Cleanup-image /Scanhealth\u003c/p\u003e\n\u003cp\u003eDISM.exe /Online /Cleanup-image /Checkhealth\u003c/p\u003e\n\u003cp\u003eDISM.exe /Online /Cleanup-image /Restorehealth\u003c/p\u003e\n\u003cp\u003esfc /scannow\u003c/p\u003e\n","upvoteCount":3,"author":"","authorId":"a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf","url":"https://learn.microsoft.com/zh-cn/answers/a/5042368"},{"@type":"Answer","id":5042358,"text":"\u003cp\u003eDISM命令提示修复完成  \u003c/p\u003e\n\u003cp\u003esfc /scannow 提示修复  \u003c/p\u003e\n\u003cp\u003e重启了，也没有效果，大佬！！\u003c/p\u003e\n\u003cp\u003e\u003ca href=\"https://filestore.community.support.microsoft.com/api/images/bf1a8f6f-6a07-4110-b420-97bfa589a092?upload=true\" rel=\"ugc nofollow\"\u003e\u003cimg src=\"https://filestore.community.support.microsoft.com/api/images/bf1a8f6f-6a07-4110-b420-97bfa589a092?upload=true\" alt=\"\" /\u003e\u003c/a\u003e\u003c/p\u003e\n","upvoteCount":2,"author":"","authorId":"a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf","url":"https://learn.microsoft.com/zh-cn/answers/a/5042358"},{"@type":"Answer","id":5042886,"text":"\u003cp\u003e那可以再试一下覆盖安装的方式更新一下系统,该操作不会影响到您的个人资料及已安装应用: \u003c/p\u003e\n\u003cp\u003e可以参考我的这篇文章中有详细的操作步骤: \u003c/p\u003e\n\u003cp\u003e\u003ca\u003ehttp://alian.fun/archives/239\u003c/a\u003e \u003c/p\u003e\n\u003cp\u003e_______________________________________________ \u003c/p\u003e\n\u003cp\u003e免责声明:以上涉及到的网址链接均[不是]来自微软的官方网站.这些页面所提供的信息均较为准确可靠.不过依然提醒您需要注意这些页面可能会包含您有可能不需要的产品广告,所以在您下载安装这些广告产品的时候请务必看清楚并确认这是您所需要的.\u003c/p\u003e\n","upvoteCount":0,"author":"","authorId":"a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf","url":"https://learn.microsoft.com/zh-cn/answers/a/5042886"}]}}
		  </script>
		</head>
	
			<body
				id="body"
				data-bi-name="body"
				class="layout-body "
				lang="zh-cn"
				dir="ltr"
			>
				<header class="layout-body-header">
		<div class="header-holder has-default-focus">
			
		<a
			href="#main"
			
			style="z-index: 1070"
			class="outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body"
			
		>
			跳转至主内容
		</a>
	

			<div hidden id="cookie-consent-holder" data-test-id="cookie-consent-container"></div>
			<!-- Unsupported browser warning -->
			<div
				id="unsupported-browser"
				style="background-color: white; color: black; padding: 16px; border-bottom: 1px solid grey;"
				hidden
			>
				<div style="max-width: 800px; margin: 0 auto;">
					<p style="font-size: 24px">此浏览器不再受支持。</p>
					<p style="font-size: 16px; margin-top: 16px;">
						请升级到 Microsoft Edge 以使用最新的功能、安全更新和技术支持。
					</p>
					<div style="margin-top: 12px;">
						<a
							href="https://go.microsoft.com/fwlink/p/?LinkID=2092881 "
							style="background-color: #0078d4; border: 1px solid #0078d4; color: white; padding: 6px 12px; border-radius: 2px; display: inline-block;"
						>
							下载 Microsoft Edge
						</a>
						<a
							href="https://learn.microsoft.com/en-us/lifecycle/faq/internet-explorer-microsoft-edge"
							style="background-color: white; padding: 6px 12px; border: 1px solid #505050; color: #171717; border-radius: 2px; display: inline-block;"
						>
							有关 Internet Explorer 和 Microsoft Edge 的详细信息
						</a>
					</div>
				</div>
			</div>
			<!-- site header -->
			<header
				id="ms--site-header"
				data-test-id="site-header-wrapper"
				role="banner"
				itemscope="itemscope"
				itemtype="http://schema.org/Organization"
			>
				<div
					id="ms--mobile-nav"
					class="site-header display-none-tablet padding-inline-none gap-none"
					data-bi-name="mobile-header"
					data-test-id="mobile-header"
				></div>
				<div
					id="ms--primary-nav"
					class="site-header display-none display-flex-tablet"
					data-bi-name="L1-header"
					data-test-id="primary-header"
				></div>
				<div
					id="ms--secondary-nav"
					class="site-header display-none display-flex-tablet"
					data-bi-name="L2-header"
					data-test-id="secondary-header"
				></div>
			</header>
			
		<!-- banner -->
		<div data-banner>
			<div id="disclaimer-holder"></div>
			
		</div>
		<!-- banner end -->
	
		</div>
	</header>
				 

				<main
					id="main"
					role="main"
					class="layout-body-main "
					data-bi-name="content"
					lang="zh-cn"
					dir="ltr"
				>
					
		<div class="layout-padding padding-block-md margin-left-auto is-reading-width">
			
			<div id="question-details" data-bi-name="qna-question-details-page-content">
				<div id="article-header" class="margin-bottom-xs">
					<div class="display-flex align-items-center">
						
		<div
			id="article-header-page-actions"
			class="margin-left-auto display-flex flex-wrap-no-wrap align-items-stretch"
		>
			
			
		<button
			class="collection button button-clear text-decoration-none has-inner-focus margin-bottom-none button-primary button-sm display-none display-inline-flex-tablet"
			id="qna-add-to-collection-button-desktop"
			data-qna-add-to-collection-button
			data-test-id="add-to-collections-button-desktop"
			data-list-type="collection"
			data-bi-name="collection"
			data-list-item-title="此问题"
			title="将此问题添加到集合"
		>
			<span class="icon margin-none" aria-hidden="true">
				<span class="docon docon-circle-addition"></span>
			</span>
			<span class="collection-status is-visually-hidden"
				>添加</span
			>
		</button>
	
			
		<details
			class="popover popover-right"
			id="article-header-page-actions-overflow"
			data-test-id="page-actions-overflow-menu-button"
		>
			<summary
				class="button button-clear text-decoration-none has-inner-focus margin-bottom-none button-sm justify-content-flex-start button-primary "
				aria-label="分享此问题"
				title="分享此问题"
			>
				<span class="icon margin-none" aria-hidden="true">
					<span class="docon docon-more-vertical"></span>
				</span>
			</summary>

			<div class="popover-content">
				
		<button
			class="collection button button-clear text-decoration-none has-inner-focus margin-bottom-none button-sm button-block justify-content-flex-start has-inner-focus margin-none display-none-tablet"
			id="qna-add-to-collection-button-mobile"
			data-qna-add-to-collection-button
			data-test-id="add-to-collections-button-mobile"
			data-list-type="collection"
			data-bi-name="collection"
			data-list-item-title="此问题"
			title="将此问题添加到集合"
		>
			<span class="icon " aria-hidden="true">
				<span class="docon docon-circle-addition"></span>
			</span>
			<span class="collection-status "
				>添加</span
			>
		</button>
	
				
		<div
			aria-hidden="true"
			class="margin-none display-none-tablet border-top"
			data-page-action-item="overflow-all"
		></div>
		<h4 class="font-size-sm padding-left-xxs">通过</h4>
		 <a
					href="https://www.facebook.com/sharer/sharer.php?u=https%3A%2F%2Flearn.microsoft.com%2Fzh-cn%2Fanswers%2Fquestions%2F3209892%2Fwindows10-64
											"
					class="button button-clear text-decoration-none has-inner-focus margin-bottom-none button-sm button-block justify-content-flex-start has-inner-focus margin-none share-facebook"
					data-bi-name="facebook"
					data-page-action-item="overflow-all"
			  >
					<span class="icon" aria-hidden="true">
						<span class="docon docon-facebook-share font-size-md color-primary"></span>
					</span>
					<span class="margin-left-xxs color-text">Facebook</span>
			  </a>
		 <a
					href="https://twitter.com/intent/tweet?original_referer=https%3A%2F%2Flearn.microsoft.com%2Fzh-cn%2Fanswers%2Fquestions%2F3209892%2Fwindows10-64
											&text=windows10-64%E7%B3%BB%E7%BB%9F%E8%B0%83%E8%8A%82%E4%BA%AE%E5%BA%A6%E9%80%89%E9%A1%B9%E6%B6%88%E5%A4%B1%EF%BC%8C%E6%97%A0%E6%B3%95%E8%B0%83%E8%8A%82%E4%BA%AE%E5%BA%A6
											&tw_p=tweetbutton&url=https%3A%2F%2Flearn.microsoft.com%2Fzh-cn%2Fanswers%2Fquestions%2F3209892%2Fwindows10-64"
					class="button button-clear text-decoration-none has-inner-focus margin-bottom-none button-sm button-block justify-content-flex-start has-inner-focus margin-none share-twitter"
					data-bi-name="twitter"
					data-page-action-item="overflow-all"
			  >
					<span class="icon" aria-hidden="true">
						<span class="docon docon-xlogo-share color-text"></span>
					</span>
					<span class="margin-left-xxs color-text">x.com 共享</span>
			  </a>
		 <a
					href="https://www.linkedin.com/cws/share?url=https%3A%2F%2Flearn.microsoft.com%2Fzh-cn%2Fanswers%2Fquestions%2F3209892%2Fwindows10-64"
					class="button button-clear text-decoration-none has-inner-focus margin-bottom-none button-sm button-block justify-content-flex-start has-inner-focus margin-none share-linkedin"
					data-bi-name="linkedin"
					data-page-action-item="overflow-all"
			  >
					<span class="icon" aria-hidden="true">
						<span class="docon docon-linked-in-logo font-size-sm color-primary"></span>
					</span>
					<span class="margin-left-xxs color-text">LinkedIn</span>
			  </a>
		
		 <a
					href="mailto:?subject=
												%5B%E5%85%B1%E4%BA%AB%E9%97%AE%E9%A2%98%5D%20windows10-64%E7%B3%BB%E7%BB%9F%E8%B0%83%E8%8A%82%E4%BA%AE%E5%BA%A6%E9%80%89%E9%A1%B9%E6%B6%88%E5%A4%B1%EF%BC%8C%E6%97%A0%E6%B3%95%E8%B0%83%E8%8A%82%E4%BA%AE%E5%BA%A6&body=windows10-64系统调节亮度选项消失，无法调节亮度https%3A%2F%2Flearn.microsoft.com%2Fzh-cn%2Fanswers%2Fquestions%2F3209892%2Fwindows10-64"
					class="button button-clear text-decoration-none has-inner-focus margin-bottom-none button-sm button-block justify-content-flex-start has-inner-focus margin-none share-email"
					data-bi-name="email"
					data-page-action-item="overflow-all"
			  >
					<span class="icon" aria-hidden="true">
						<span class="docon docon-mail-message font-size-sm color-primary"></span>
					</span>
					<span class="margin-left-xxs color-text">电子邮件</span>
			  </a>
		<!-- copy link -->
		
	
			</div>
		</details>
	
		</div>
	
					</div>
				</div>
				<div id="thread-history-container" class="padding-block-md" hidden></div>
				
		
		
		
		<h1 class="title is-2">windows10-64系统调节亮度选项消失，无法调节亮度</h1>
		<div class="display-flex flex-direction-row">
			
			
		</div>
		<div class="level margin-none align-items-flex-start">
			
			<div class="level-left flex-direction-column" data-id="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf">
				<div class="display-flex align-items-center">
					  <div class="image is-32x32 margin-right-xxs">
					
		<img
			class="border-radius-rounded background-color-body-medium"
			loading="eager"
			alt=""
			src="data:image/svg+xml, %3Csvg xmlns=&#39;http://www.w3.org/2000/svg&#39; height=&#39;64&#39; class=&#39;font-weight-bold&#39; style=&#39;font: 600 30.11764705882353px &quot;SegoeUI&quot;, Arial&#39; width=&#39;64&#39;%3E%3Ccircle fill=&#39;hsl(128, 8%, 22%)&#39; cx=&#39;32&#39; cy=&#39;32&#39; r=&#39;32&#39; /%3E%3Ctext x=&#39;50%25&#39; y=&#39;55%25&#39; dominant-baseline=&#39;middle&#39; text-anchor=&#39;middle&#39; fill=&#39;%23FFF&#39; %3EA%3C/text%3E%3C/svg%3E"
		/>
	
			  </div> <div class="has-line-height-reset has-text-wrap width-250">
			<span
						class="has-text-subtle font-weight-semibold padding-right-xxs font-size-sm"
				  >
						匿名
				  </span>
			
			
		</div>
				</div>
			</div>
			<div class="level-right margin-top-none">
				<span class="font-size-xs"> <local-time format="datetime" datetime="2019-08-29T08:06:09+00:00" class="is-visually-hidden">2019-08-29T08:06:09+00:00</local-time> </span>
			</div>
		
		</div>
		
					<div class="content margin-top-xxs"><p>操作体统:win10-64家庭版 </p>
<p>版本号：1903</p>
<p>调节亮度选项消失时间大概在7月中旬</p>
<p>     以前一直没出过问题，出问题前曾更新过驱动，在安装的时候屏幕卡屏了一下，后来发现没有‘’调节亮度‘’这个选项了，右键电源，电源选项中的下方，都没有这个选项了，亮度也无法调节，电源选项的小太阳图标灰色，无法调节，windows 移动中心的那个亮度调节可以拖动，但是屏幕亮度没有变化。</p>
<p>    在网上百度过，改注册表，重新装驱动，都没有用，后来用windows10自带的重置电脑，也没有用。</p>
<p><a href="https://filestore.community.support.microsoft.com/api/images/f4efc3b0-7e0d-48eb-9b22-7ad72f884d58?upload=true" rel="ugc nofollow"><img src="https://filestore.community.support.microsoft.com/api/images/f4efc3b0-7e0d-48eb-9b22-7ad72f884d58?upload=true" alt="" /></a></p>
<p><a href="https://filestore.community.support.microsoft.com/api/images/f15ce0bd-3be1-4770-8dc2-28b32379b4c5?upload=true" rel="ugc nofollow"><img src="https://filestore.community.support.microsoft.com/api/images/f15ce0bd-3be1-4770-8dc2-28b32379b4c5?upload=true" alt="" /></a><a href="https://filestore.community.support.microsoft.com/api/images/b94799c9-2ad5-47c8-b7fb-8caa63040928?upload=true" rel="ugc nofollow"><img src="https://filestore.community.support.microsoft.com/api/images/b94799c9-2ad5-47c8-b7fb-8caa63040928?upload=true" alt="" /></a></p>
</div>
					<div class="display-flex flex-wrap-wrap gap-xxs margin-block-xs">
						
		<span class="tag tag-filled tag-interactive">
			<details class="popover tag-popover popover-left">
				<summary class="tag-summary has-text-wrap tag-sm" data-test-id="question-tag-windows-home-windows-10-platform-devices-drivers">
					<span>Windows 家庭版 | Windows 10 | 设备和驱动程序</span>
				</summary>
				<div class="popover-content width-350-tablet padding-none" data-test-id="tag-card-popover">
					<article class="card border-left-lg border-left-color-accent">
		<div class="card-content margin-bottom-none">
			<a
				class="card-title has-text-wrap"
				data-test-id="tag-card-title-windows-home-windows-10-platform-devices-drivers"
				data-bi-name="tag-card-link"
				href="/zh-cn/answers/tags/1149/windows-home-windows-10-platform-devices-drivers/"
			>
				Windows 家庭版 | Windows 10 | 设备和驱动程序
			</a>
			<div class="card-content-description" data-test-id="tag-card-description">
				
			</div>
		</div>
		<div class="card-template">
			<img alt="" class="card-template-icon image is-32x32" src=https://learn.microsoft.com/en-us/media/logos/logo_windows.svg />
			<div class="card-template-detail">
				<span class="font-size-sm font-weight-semilight" data-test-id="tag-card-count-windows-home-windows-10-platform-devices-drivers">
					10,740 个问题
				</span>
			</div>
		</div>
		<div class="card-footer">
			<div class="card-footer-item">
				<div class="buttons">
					<button
						class="not-authenticated link-button font-size-sm margin-block-xs docs-sign-in"
						data-bi-name="follow-tag-sign-in"
						data-test-id="tag-card-follow-button-windows-home-windows-10-platform-devices-drivers"
					>
						登录以关注
					</button>
					 <form
		action="/api/profiles/follow/tag/zh-cn+aHR0cHM6Ly9taWNyb3NvZnQtZGV2cmVsLnBvb2xwYXJ0eS5iaXovUW5BQ29tcG91bmQvZGIwNjY0MjUtNjY0Mi00YWI2LThhYjgtNDZiNDExNjQwZGJi"
		method="POST"
		new
		novalidate
		hidden
		
		data-follow-tag-id=zh-cn+aHR0cHM6Ly9taWNyb3NvZnQtZGV2cmVsLnBvb2xwYXJ0eS5iaXovUW5BQ29tcG91bmQvZGIwNjY0MjUtNjY0Mi00YWI2LThhYjgtNDZiNDExNjQwZGJi
		
		class="authenticated "
	>
		<form-behavior
			navigation="reload"
			navigation-href="empty"
			header-content-type="application/json"
			header-X-DocsAuth="cookie"
			new
		></form-behavior>
		<button
		class="
					button button-primary
					
					button-sm margin-block-xxs
				"
		data-bi-name=follow-tag
		type="submit"
	>
		关注
	</button>
	</form>
				</div>
			</div>
		</div>
	</article>
				</div>
			</details>
		</span>
	
					</div>
			  
		
					<div class="notification">
						<div class="notification-content display-flex flex-direction-row">
							<p class="notification-title">
								<span class="icon" aria-hidden="true">
									<span class="docon docon-lock"></span>
								</span>
							</p>
							<p>
								<span>锁定的问题。</span>
								<span
									>此问题已从 Microsoft 支持社区迁移。 你可投票决定它是否有用，但不能添加评论或回复，也不能关注问题。 为了保护隐私，对于已迁移的问题，用户个人资料是匿名的。</span
								>
							</p>
						</div>
					</div>
			  
		<div class="margin-top-xxs">
			<div class="level">
				<div class="level-left">
					<div class="display-none-tablet padding-top-xxs margin-right-xxs">
						
					</div>
					<div class="buttons">
						
				<span
					class="button button-clear text-decoration-none has-inner-focus margin-bottom-none font-size-md color-text-subtle align-self-flex-start"
					title="无注释"
				>
					<span class="icon" aria-hidden="true">
						<span class="docon docon-comment-outline"></span>
					</span>
					<span class="font-size-sm is-visually-hidden-mobile">0 个注释</span>
					<span class="visually-hidden">无注释</span>
				</span>
		  
						
		
		
		<button
			title="报告问题"
			class="button button-clear text-decoration-none has-inner-focus margin-bottom-none button-primary qna-report-post-button  "
			data-list-type="report"
			data-bi-name="report"
			data-reportedUserId="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf"
			data-questionId="3209892"
			data-entityId="3209892"
			data-entityType="question"
			data-action="/api/questions/moderation/reportconcern?_method=PUT&amp;locale=zh-cn"
			data-page-action-item="overflow-mobile"
			data-test-id="report-post-button-question-3209892"
		>
			<span class="icon " aria-hidden="true">
				<span class="docon docon-flag"></span>
			</span>
			<span class="font-size-sm is-visually-hidden-mobile"
				>报告问题</span
			>
		</button>
	
		
		
	
					</div>
				</div>
				<div class="level-right is-full-height">
					 <div id="question-vote">
								<button
									title="你有相同或类似的问题"
									class="not-authenticated link-button font-size-sm sign-in-modal"
								>
									我有相同的问题
								</button>
								<button
									id="question-vote-button"
									title="你有相同或类似的问题"
									class="authenticated link-button font-size-sm "
									data-id="3209892"
									data-vote-count="96"
									data-bi-name="question-vote-up"
								>
									我有相同的问题
								</button>
								<span
									id="question-vote-total"
									class="badge badge-filled badge-sm margin-left-xxs "
								>
									<span class="vote-count" aria-hidden="true">96</span>
									<span class="vote-text visually-hidden"
										>{count} 票</span
									>
								</span>
						  </div>
				</div>
			</div>
			<div class="question-vote-error"></div>
			
		<div id="question-3209892-expander" class="width-full">
			<div class="expandable is-expanded">
				<div id="question-3209892-moreThan">
					
		<ol>
			
		</ol>
	
				</div>
				
			</div>
		</div>
	
			
		</div>
	
			</div>
			<div id="answers" class="column margin-top-sm is-full">
				<div class="margin-top-xxs">
					
		
		<div class="level">
			<div class="level-left">
				<h2 class="title is-4">9 个答案</h2>
			</div>
			<div class="level-right">
				
		<details class="popover">
			<summary class="button button-clear" data-test-id="sortby-popover-button">
				<span
					>排序依据：
		<span class="has-text-primary font-weight-semibold">
			非常有帮助
			<span class="icon" aria-hidden="true">
				<span class="expanded-indicator docon docon-chevron-down-light"></span>
			</span>
		</span></span
				>
			</summary>
			<div class="popover-content padding-none" data-test-id="sortby-menu">
				 <a
			aria-label="排序依据：非常有帮助"
			class="button button-clear has-inner-focus button-block text-decoration-none justify-content-flex-start is-hovered"
			aria-current=page
			href="?orderby=helpful&amp;page=1&amp;forum=windows-all&amp;referrer=answers#answers"
			data-test-id="sortby-filter-link-helpful"
			>非常有帮助</a
		> <a
			aria-label="排序依据：最新"
			class="button button-clear has-inner-focus button-block text-decoration-none justify-content-flex-start "
			
			href="?orderby=newest&amp;page=1&amp;forum=windows-all&amp;referrer=answers#answers"
			data-test-id="sortby-filter-link-newest"
			>最新</a
		> <a
			aria-label="排序依据：最早"
			class="button button-clear has-inner-focus button-block text-decoration-none justify-content-flex-start "
			
			href="?orderby=oldest&amp;page=1&amp;forum=windows-all&amp;referrer=answers#answers"
			data-test-id="sortby-filter-link-oldest"
			>最早</a
		>
			</div>
		</details>
	
			</div>
		</div>
		<ol class="list-style-none">
			
			<li
				id="answer-5042357"
				class="margin-bottom-sm padding-block-sm border border-radius-lg"
				itemprop="suggestedAnswer"
				itemscope
				itemtype="https://schema.org/Answer0"
				data-test-id="answer-5042357"
			>
				<div
					class="columns is-multiline margin-left-xs margin-right-xs"
				>
					<div class="column is-12">
		
		<div class="level margin-none align-items-flex-start">
			
			<div class="level-left flex-direction-column" data-id="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf">
				<div class="display-flex align-items-center">
					  <div class="image is-32x32 margin-right-xxs">
					
		<img
			class="border-radius-rounded background-color-body-medium"
			loading="lazy"
			alt=""
			src="data:image/svg+xml, %3Csvg xmlns=&#39;http://www.w3.org/2000/svg&#39; height=&#39;64&#39; class=&#39;font-weight-bold&#39; style=&#39;font: 600 30.11764705882353px &quot;SegoeUI&quot;, Arial&#39; width=&#39;64&#39;%3E%3Ccircle fill=&#39;hsl(128, 8%, 22%)&#39; cx=&#39;32&#39; cy=&#39;32&#39; r=&#39;32&#39; /%3E%3Ctext x=&#39;50%25&#39; y=&#39;55%25&#39; dominant-baseline=&#39;middle&#39; text-anchor=&#39;middle&#39; fill=&#39;%23FFF&#39; %3EA%3C/text%3E%3C/svg%3E"
		/>
	
			  </div> <div class="has-line-height-reset has-text-wrap width-250">
			<span
						class="has-text-subtle font-weight-semibold padding-right-xxs font-size-sm"
				  >
						匿名
				  </span>
			
			
		</div>
				</div>
			</div>
			<div class="level-right margin-top-none">
				<span class="font-size-xs"> <local-time format="datetime" datetime="2019-08-29T08:08:17+00:00" class="is-visually-hidden">2019-08-29T08:08:17+00:00</local-time> </span>
			</div>
		
		</div>
		
		<div>
			 <div class="content" itemprop="text"><p>您好,我是独立顾问(Independent Advisor)Dexter,请让我来帮助您.</p>
<p>请您先右键点击开始按钮&gt;运行&gt;输入winver回车查看一下系统版本的具体信息,可以截图回复一下</p>
</div> 
		</div>
		<div>
			
			<div
		class="margin-block-xxs padding-xs background-color-body-medium font-size-sm display-flex flex-direction-column border-radius-lg"
	>
		<div class="display-flex justify-content-space-between flex-wrap-wrap align-items-baseline">
			<div class="display-flex flex-grow-1">
				 <form
		method="POST"
		id="answer-rating-form-5042357"
		data-answer-id="5042357"
		new
		novalidate
		class="width-full"
	>
		<form-behavior header-X-DocsAuth="cookie" new></form-behavior>
		<div
			class="display-flex justify-content-space-between align-items-center flex-wrap-wrap"
		>
			
						<div
							class="field display-inline-flex flex-wrap-wrap align-items-center margin-bottom-none"
						>
							<label
								class="field-label margin-bottom-none margin-right-xs"
								for="answer-helpful-rating-5042357"
								>
				<a
					class="not-authenticated link-button font-size-sm margin-right-sm docs-sign-in"
					data-bi-name="answer-rating-sign-in"
					href="#"
				>
					请登录以评价此答案。
				</a>
		  
							</label>
							<div
								id="answer-helpful-rating-5042357"
								class="field-body flex-grow-0 margin-top-none"
							>
								
		<button
			id="answer-rating-5042357-yes"
			class="authenticated thumb-rating button button-primary button-sm  like"
			type="submit"
			form="answer-rating-form-5042357"
			formaction="/api/questions/answers/5042357/helpful/yes?_method=PUT&amp;locale=zh-cn"
			data-action=/api/questions/answers/5042357/accept?_method=PUT&amp;locale=zh-cn
			data-answer-rating-button
			data-bi-name="answer-rating-yes"
			title="此答案很有用"
			
		>
			<span aria-hidden="true" class="icon docon docon-like"></span>
			<span>是</span>
		</button>
	
								
		<button
			id="answer-rating-5042357-no"
			class="authenticated thumb-rating button button-primary button-sm  dislike"
			type="submit"
			form="answer-rating-form-5042357"
			formaction="/api/questions/answers/5042357/helpful/no?_method=PUT&amp;locale=zh-cn"
			
			data-answer-rating-button
			data-bi-name="answer-rating-no"
			title="此答案没有用处"
			
		>
			<span aria-hidden="true" class="icon docon docon-dislike"></span>
			<span>否</span>
		</button>
	
							</div>
						</div>
				  
			<span class="margin-top-xxs margin-top-none-tablet" id="answer-helpful-text-5042357"
				>4 个人认为此答案很有帮助。</span
			>
		</div>
	</form>
			</div>
		</div>
		
	</div>
			<div class="buttons margin-block-xxs margin-top-xs">
				
				<span
					class="button button-clear text-decoration-none has-inner-focus margin-bottom-none font-size-md color-text-subtle align-self-flex-start"
					title="无注释"
				>
					<span class="icon" aria-hidden="true">
						<span class="docon docon-comment-outline"></span>
					</span>
					<span class="font-size-sm is-visually-hidden-mobile">0 个注释</span>
					<span class="visually-hidden">无注释</span>
				</span>
		  
				
		
		
		<button
			title="报告问题"
			class="button button-clear text-decoration-none has-inner-focus margin-bottom-none button-primary qna-report-post-button  "
			data-list-type="report"
			data-bi-name="report"
			data-reportedUserId="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf"
			data-questionId="3209892"
			data-entityId="5042357"
			data-entityType="answer"
			data-action="/api/questions/moderation/reportconcern?_method=PUT&amp;locale=zh-cn"
			data-page-action-item="overflow-mobile"
			data-test-id="report-post-button-answer-5042357"
		>
			<span class="icon " aria-hidden="true">
				<span class="docon docon-flag"></span>
			</span>
			<span class="font-size-sm is-visually-hidden-mobile"
				>报告问题</span
			>
		</button>
	
		
		
	
			</div>
			
		<div id="question-answer0-5042357-expander" class="width-full">
			<div class="expandable ">
				<div id="question-answer0-5042357-moreThan">
					
		<ol>
			
		</ol>
	
				</div>
				
			</div>
		</div>
	
			
		</div>
	</div>
				</div>
			</li>
		 
			<li
				id="answer-5042873"
				class="margin-bottom-sm padding-block-sm border border-radius-lg"
				itemprop="suggestedAnswer"
				itemscope
				itemtype="https://schema.org/Answer1"
				data-test-id="answer-5042873"
			>
				<div
					class="columns is-multiline margin-left-xs margin-right-xs"
				>
					<div class="column is-12">
		
		<div class="level margin-none align-items-flex-start">
			
			<div class="level-left flex-direction-column" data-id="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf">
				<div class="display-flex align-items-center">
					  <div class="image is-32x32 margin-right-xxs">
					
		<img
			class="border-radius-rounded background-color-body-medium"
			loading="lazy"
			alt=""
			src="data:image/svg+xml, %3Csvg xmlns=&#39;http://www.w3.org/2000/svg&#39; height=&#39;64&#39; class=&#39;font-weight-bold&#39; style=&#39;font: 600 30.11764705882353px &quot;SegoeUI&quot;, Arial&#39; width=&#39;64&#39;%3E%3Ccircle fill=&#39;hsl(128, 8%, 22%)&#39; cx=&#39;32&#39; cy=&#39;32&#39; r=&#39;32&#39; /%3E%3Ctext x=&#39;50%25&#39; y=&#39;55%25&#39; dominant-baseline=&#39;middle&#39; text-anchor=&#39;middle&#39; fill=&#39;%23FFF&#39; %3EA%3C/text%3E%3C/svg%3E"
		/>
	
			  </div> <div class="has-line-height-reset has-text-wrap width-250">
			<span
						class="has-text-subtle font-weight-semibold padding-right-xxs font-size-sm"
				  >
						匿名
				  </span>
			
			
		</div>
				</div>
			</div>
			<div class="level-right margin-top-none">
				<span class="font-size-xs"> <local-time format="datetime" datetime="2019-08-29T08:12:02+00:00" class="is-visually-hidden">2019-08-29T08:12:02+00:00</local-time> </span>
			</div>
		
		</div>
		
		<div>
			 <div class="content" itemprop="text"><p>......     <a href="https://filestore.community.support.microsoft.com/api/images/50853dc4-7247-4c21-a9a8-23ad2559413f?upload=true" rel="ugc nofollow"><img src="https://filestore.community.support.microsoft.com/api/images/50853dc4-7247-4c21-a9a8-23ad2559413f?upload=true" alt="" /></a></p>
</div> 
		</div>
		<div>
			
			<div
		class="margin-block-xxs padding-xs background-color-body-medium font-size-sm display-flex flex-direction-column border-radius-lg"
	>
		<div class="display-flex justify-content-space-between flex-wrap-wrap align-items-baseline">
			<div class="display-flex flex-grow-1">
				 <form
		method="POST"
		id="answer-rating-form-5042873"
		data-answer-id="5042873"
		new
		novalidate
		class="width-full"
	>
		<form-behavior header-X-DocsAuth="cookie" new></form-behavior>
		<div
			class="display-flex justify-content-space-between align-items-center flex-wrap-wrap"
		>
			
						<div
							class="field display-inline-flex flex-wrap-wrap align-items-center margin-bottom-none"
						>
							<label
								class="field-label margin-bottom-none margin-right-xs"
								for="answer-helpful-rating-5042873"
								>
				<a
					class="not-authenticated link-button font-size-sm margin-right-sm docs-sign-in"
					data-bi-name="answer-rating-sign-in"
					href="#"
				>
					请登录以评价此答案。
				</a>
		  
							</label>
							<div
								id="answer-helpful-rating-5042873"
								class="field-body flex-grow-0 margin-top-none"
							>
								
		<button
			id="answer-rating-5042873-yes"
			class="authenticated thumb-rating button button-primary button-sm  like"
			type="submit"
			form="answer-rating-form-5042873"
			formaction="/api/questions/answers/5042873/helpful/yes?_method=PUT&amp;locale=zh-cn"
			data-action=/api/questions/answers/5042873/accept?_method=PUT&amp;locale=zh-cn
			data-answer-rating-button
			data-bi-name="answer-rating-yes"
			title="此答案很有用"
			
		>
			<span aria-hidden="true" class="icon docon docon-like"></span>
			<span>是</span>
		</button>
	
								
		<button
			id="answer-rating-5042873-no"
			class="authenticated thumb-rating button button-primary button-sm  dislike"
			type="submit"
			form="answer-rating-form-5042873"
			formaction="/api/questions/answers/5042873/helpful/no?_method=PUT&amp;locale=zh-cn"
			
			data-answer-rating-button
			data-bi-name="answer-rating-no"
			title="此答案没有用处"
			
		>
			<span aria-hidden="true" class="icon docon docon-dislike"></span>
			<span>否</span>
		</button>
	
							</div>
						</div>
				  
			<span class="margin-top-xxs margin-top-none-tablet" id="answer-helpful-text-5042873"
				>3 个人认为此答案很有帮助。</span
			>
		</div>
	</form>
			</div>
		</div>
		
	</div>
			<div class="buttons margin-block-xxs margin-top-xs">
				
				<span
					class="button button-clear text-decoration-none has-inner-focus margin-bottom-none font-size-md color-text-subtle align-self-flex-start"
					title="无注释"
				>
					<span class="icon" aria-hidden="true">
						<span class="docon docon-comment-outline"></span>
					</span>
					<span class="font-size-sm is-visually-hidden-mobile">0 个注释</span>
					<span class="visually-hidden">无注释</span>
				</span>
		  
				
		
		
		<button
			title="报告问题"
			class="button button-clear text-decoration-none has-inner-focus margin-bottom-none button-primary qna-report-post-button  "
			data-list-type="report"
			data-bi-name="report"
			data-reportedUserId="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf"
			data-questionId="3209892"
			data-entityId="5042873"
			data-entityType="answer"
			data-action="/api/questions/moderation/reportconcern?_method=PUT&amp;locale=zh-cn"
			data-page-action-item="overflow-mobile"
			data-test-id="report-post-button-answer-5042873"
		>
			<span class="icon " aria-hidden="true">
				<span class="docon docon-flag"></span>
			</span>
			<span class="font-size-sm is-visually-hidden-mobile"
				>报告问题</span
			>
		</button>
	
		
		
	
			</div>
			
		<div id="question-answer1-5042873-expander" class="width-full">
			<div class="expandable ">
				<div id="question-answer1-5042873-moreThan">
					
		<ol>
			
		</ol>
	
				</div>
				
			</div>
		</div>
	
			
		</div>
	</div>
				</div>
			</li>
		 
			<li
				id="answer-5042368"
				class="margin-bottom-sm padding-block-sm border border-radius-lg"
				itemprop="suggestedAnswer"
				itemscope
				itemtype="https://schema.org/Answer2"
				data-test-id="answer-5042368"
			>
				<div
					class="columns is-multiline margin-left-xs margin-right-xs"
				>
					<div class="column is-12">
		
		<div class="level margin-none align-items-flex-start">
			
			<div class="level-left flex-direction-column" data-id="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf">
				<div class="display-flex align-items-center">
					  <div class="image is-32x32 margin-right-xxs">
					
		<img
			class="border-radius-rounded background-color-body-medium"
			loading="lazy"
			alt=""
			src="data:image/svg+xml, %3Csvg xmlns=&#39;http://www.w3.org/2000/svg&#39; height=&#39;64&#39; class=&#39;font-weight-bold&#39; style=&#39;font: 600 30.11764705882353px &quot;SegoeUI&quot;, Arial&#39; width=&#39;64&#39;%3E%3Ccircle fill=&#39;hsl(128, 8%, 22%)&#39; cx=&#39;32&#39; cy=&#39;32&#39; r=&#39;32&#39; /%3E%3Ctext x=&#39;50%25&#39; y=&#39;55%25&#39; dominant-baseline=&#39;middle&#39; text-anchor=&#39;middle&#39; fill=&#39;%23FFF&#39; %3EA%3C/text%3E%3C/svg%3E"
		/>
	
			  </div> <div class="has-line-height-reset has-text-wrap width-250">
			<span
						class="has-text-subtle font-weight-semibold padding-right-xxs font-size-sm"
				  >
						匿名
				  </span>
			
			
		</div>
				</div>
			</div>
			<div class="level-right margin-top-none">
				<span class="font-size-xs"> <local-time format="datetime" datetime="2019-08-29T10:53:41+00:00" class="is-visually-hidden">2019-08-29T10:53:41+00:00</local-time> </span>
			</div>
		
		</div>
		
		<div>
			 <div class="content" itemprop="text"><p>嗯,已经更新到最新的版本了,建议您可以参考下面的步骤对系统镜像进行校验和修复试试:</p>
<ol>
<li> 按键盘Win+R打开&quot;运行&quot;,输入cmd然后按Ctrl+Shift+回车,以管理员身份打开命令提示符</li>
<li> 在打开的命令提示符窗口中粘贴运行下面的命令:</li>
</ol>
<p>DISM.exe /Online /Cleanup-image /Scanhealth</p>
<p>DISM.exe /Online /Cleanup-image /Checkhealth</p>
<p>DISM.exe /Online /Cleanup-image /Restorehealth</p>
<p>sfc /scannow</p>
</div> 
		</div>
		<div>
			
			<div
		class="margin-block-xxs padding-xs background-color-body-medium font-size-sm display-flex flex-direction-column border-radius-lg"
	>
		<div class="display-flex justify-content-space-between flex-wrap-wrap align-items-baseline">
			<div class="display-flex flex-grow-1">
				 <form
		method="POST"
		id="answer-rating-form-5042368"
		data-answer-id="5042368"
		new
		novalidate
		class="width-full"
	>
		<form-behavior header-X-DocsAuth="cookie" new></form-behavior>
		<div
			class="display-flex justify-content-space-between align-items-center flex-wrap-wrap"
		>
			
						<div
							class="field display-inline-flex flex-wrap-wrap align-items-center margin-bottom-none"
						>
							<label
								class="field-label margin-bottom-none margin-right-xs"
								for="answer-helpful-rating-5042368"
								>
				<a
					class="not-authenticated link-button font-size-sm margin-right-sm docs-sign-in"
					data-bi-name="answer-rating-sign-in"
					href="#"
				>
					请登录以评价此答案。
				</a>
		  
							</label>
							<div
								id="answer-helpful-rating-5042368"
								class="field-body flex-grow-0 margin-top-none"
							>
								
		<button
			id="answer-rating-5042368-yes"
			class="authenticated thumb-rating button button-primary button-sm  like"
			type="submit"
			form="answer-rating-form-5042368"
			formaction="/api/questions/answers/5042368/helpful/yes?_method=PUT&amp;locale=zh-cn"
			data-action=/api/questions/answers/5042368/accept?_method=PUT&amp;locale=zh-cn
			data-answer-rating-button
			data-bi-name="answer-rating-yes"
			title="此答案很有用"
			
		>
			<span aria-hidden="true" class="icon docon docon-like"></span>
			<span>是</span>
		</button>
	
								
		<button
			id="answer-rating-5042368-no"
			class="authenticated thumb-rating button button-primary button-sm  dislike"
			type="submit"
			form="answer-rating-form-5042368"
			formaction="/api/questions/answers/5042368/helpful/no?_method=PUT&amp;locale=zh-cn"
			
			data-answer-rating-button
			data-bi-name="answer-rating-no"
			title="此答案没有用处"
			
		>
			<span aria-hidden="true" class="icon docon docon-dislike"></span>
			<span>否</span>
		</button>
	
							</div>
						</div>
				  
			<span class="margin-top-xxs margin-top-none-tablet" id="answer-helpful-text-5042368"
				>3 个人认为此答案很有帮助。</span
			>
		</div>
	</form>
			</div>
		</div>
		
	</div>
			<div class="buttons margin-block-xxs margin-top-xs">
				
				<span
					class="button button-clear text-decoration-none has-inner-focus margin-bottom-none font-size-md color-text-subtle align-self-flex-start"
					title="无注释"
				>
					<span class="icon" aria-hidden="true">
						<span class="docon docon-comment-outline"></span>
					</span>
					<span class="font-size-sm is-visually-hidden-mobile">0 个注释</span>
					<span class="visually-hidden">无注释</span>
				</span>
		  
				
		
		
		<button
			title="报告问题"
			class="button button-clear text-decoration-none has-inner-focus margin-bottom-none button-primary qna-report-post-button  "
			data-list-type="report"
			data-bi-name="report"
			data-reportedUserId="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf"
			data-questionId="3209892"
			data-entityId="5042368"
			data-entityType="answer"
			data-action="/api/questions/moderation/reportconcern?_method=PUT&amp;locale=zh-cn"
			data-page-action-item="overflow-mobile"
			data-test-id="report-post-button-answer-5042368"
		>
			<span class="icon " aria-hidden="true">
				<span class="docon docon-flag"></span>
			</span>
			<span class="font-size-sm is-visually-hidden-mobile"
				>报告问题</span
			>
		</button>
	
		
		
	
			</div>
			
		<div id="question-answer2-5042368-expander" class="width-full">
			<div class="expandable ">
				<div id="question-answer2-5042368-moreThan">
					
		<ol>
			
		</ol>
	
				</div>
				
			</div>
		</div>
	
			
		</div>
	</div>
				</div>
			</li>
		 
			<li
				id="answer-5042358"
				class="margin-bottom-sm padding-block-sm border border-radius-lg"
				itemprop="suggestedAnswer"
				itemscope
				itemtype="https://schema.org/Answer3"
				data-test-id="answer-5042358"
			>
				<div
					class="columns is-multiline margin-left-xs margin-right-xs"
				>
					<div class="column is-12">
		
		<div class="level margin-none align-items-flex-start">
			
			<div class="level-left flex-direction-column" data-id="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf">
				<div class="display-flex align-items-center">
					  <div class="image is-32x32 margin-right-xxs">
					
		<img
			class="border-radius-rounded background-color-body-medium"
			loading="lazy"
			alt=""
			src="data:image/svg+xml, %3Csvg xmlns=&#39;http://www.w3.org/2000/svg&#39; height=&#39;64&#39; class=&#39;font-weight-bold&#39; style=&#39;font: 600 30.11764705882353px &quot;SegoeUI&quot;, Arial&#39; width=&#39;64&#39;%3E%3Ccircle fill=&#39;hsl(128, 8%, 22%)&#39; cx=&#39;32&#39; cy=&#39;32&#39; r=&#39;32&#39; /%3E%3Ctext x=&#39;50%25&#39; y=&#39;55%25&#39; dominant-baseline=&#39;middle&#39; text-anchor=&#39;middle&#39; fill=&#39;%23FFF&#39; %3EA%3C/text%3E%3C/svg%3E"
		/>
	
			  </div> <div class="has-line-height-reset has-text-wrap width-250">
			<span
						class="has-text-subtle font-weight-semibold padding-right-xxs font-size-sm"
				  >
						匿名
				  </span>
			
			
		</div>
				</div>
			</div>
			<div class="level-right margin-top-none">
				<span class="font-size-xs"> <local-time format="datetime" datetime="2019-08-30T04:48:43+00:00" class="is-visually-hidden">2019-08-30T04:48:43+00:00</local-time> </span>
			</div>
		
		</div>
		
		<div>
			 <div class="content" itemprop="text"><p>DISM命令提示修复完成  </p>
<p>sfc /scannow 提示修复  </p>
<p>重启了，也没有效果，大佬！！</p>
<p><a href="https://filestore.community.support.microsoft.com/api/images/bf1a8f6f-6a07-4110-b420-97bfa589a092?upload=true" rel="ugc nofollow"><img src="https://filestore.community.support.microsoft.com/api/images/bf1a8f6f-6a07-4110-b420-97bfa589a092?upload=true" alt="" /></a></p>
</div> 
		</div>
		<div>
			
			<div
		class="margin-block-xxs padding-xs background-color-body-medium font-size-sm display-flex flex-direction-column border-radius-lg"
	>
		<div class="display-flex justify-content-space-between flex-wrap-wrap align-items-baseline">
			<div class="display-flex flex-grow-1">
				 <form
		method="POST"
		id="answer-rating-form-5042358"
		data-answer-id="5042358"
		new
		novalidate
		class="width-full"
	>
		<form-behavior header-X-DocsAuth="cookie" new></form-behavior>
		<div
			class="display-flex justify-content-space-between align-items-center flex-wrap-wrap"
		>
			
						<div
							class="field display-inline-flex flex-wrap-wrap align-items-center margin-bottom-none"
						>
							<label
								class="field-label margin-bottom-none margin-right-xs"
								for="answer-helpful-rating-5042358"
								>
				<a
					class="not-authenticated link-button font-size-sm margin-right-sm docs-sign-in"
					data-bi-name="answer-rating-sign-in"
					href="#"
				>
					请登录以评价此答案。
				</a>
		  
							</label>
							<div
								id="answer-helpful-rating-5042358"
								class="field-body flex-grow-0 margin-top-none"
							>
								
		<button
			id="answer-rating-5042358-yes"
			class="authenticated thumb-rating button button-primary button-sm  like"
			type="submit"
			form="answer-rating-form-5042358"
			formaction="/api/questions/answers/5042358/helpful/yes?_method=PUT&amp;locale=zh-cn"
			data-action=/api/questions/answers/5042358/accept?_method=PUT&amp;locale=zh-cn
			data-answer-rating-button
			data-bi-name="answer-rating-yes"
			title="此答案很有用"
			
		>
			<span aria-hidden="true" class="icon docon docon-like"></span>
			<span>是</span>
		</button>
	
								
		<button
			id="answer-rating-5042358-no"
			class="authenticated thumb-rating button button-primary button-sm  dislike"
			type="submit"
			form="answer-rating-form-5042358"
			formaction="/api/questions/answers/5042358/helpful/no?_method=PUT&amp;locale=zh-cn"
			
			data-answer-rating-button
			data-bi-name="answer-rating-no"
			title="此答案没有用处"
			
		>
			<span aria-hidden="true" class="icon docon docon-dislike"></span>
			<span>否</span>
		</button>
	
							</div>
						</div>
				  
			<span class="margin-top-xxs margin-top-none-tablet" id="answer-helpful-text-5042358"
				>2 个人认为此答案很有帮助。</span
			>
		</div>
	</form>
			</div>
		</div>
		
	</div>
			<div class="buttons margin-block-xxs margin-top-xs">
				
				<span
					class="button button-clear text-decoration-none has-inner-focus margin-bottom-none font-size-md color-text-subtle align-self-flex-start"
					title="无注释"
				>
					<span class="icon" aria-hidden="true">
						<span class="docon docon-comment-outline"></span>
					</span>
					<span class="font-size-sm is-visually-hidden-mobile">0 个注释</span>
					<span class="visually-hidden">无注释</span>
				</span>
		  
				
		
		
		<button
			title="报告问题"
			class="button button-clear text-decoration-none has-inner-focus margin-bottom-none button-primary qna-report-post-button  "
			data-list-type="report"
			data-bi-name="report"
			data-reportedUserId="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf"
			data-questionId="3209892"
			data-entityId="5042358"
			data-entityType="answer"
			data-action="/api/questions/moderation/reportconcern?_method=PUT&amp;locale=zh-cn"
			data-page-action-item="overflow-mobile"
			data-test-id="report-post-button-answer-5042358"
		>
			<span class="icon " aria-hidden="true">
				<span class="docon docon-flag"></span>
			</span>
			<span class="font-size-sm is-visually-hidden-mobile"
				>报告问题</span
			>
		</button>
	
		
		
	
			</div>
			
		<div id="question-answer3-5042358-expander" class="width-full">
			<div class="expandable ">
				<div id="question-answer3-5042358-moreThan">
					
		<ol>
			
		</ol>
	
				</div>
				
			</div>
		</div>
	
			
		</div>
	</div>
				</div>
			</li>
		 
			<li
				id="answer-5042886"
				class="margin-bottom-sm padding-block-sm border border-radius-lg"
				itemprop="suggestedAnswer"
				itemscope
				itemtype="https://schema.org/Answer4"
				data-test-id="answer-5042886"
			>
				<div
					class="columns is-multiline margin-left-xs margin-right-xs"
				>
					<div class="column is-12">
		
		<div class="level margin-none align-items-flex-start">
			
			<div class="level-left flex-direction-column" data-id="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf">
				<div class="display-flex align-items-center">
					  <div class="image is-32x32 margin-right-xxs">
					
		<img
			class="border-radius-rounded background-color-body-medium"
			loading="lazy"
			alt=""
			src="data:image/svg+xml, %3Csvg xmlns=&#39;http://www.w3.org/2000/svg&#39; height=&#39;64&#39; class=&#39;font-weight-bold&#39; style=&#39;font: 600 30.11764705882353px &quot;SegoeUI&quot;, Arial&#39; width=&#39;64&#39;%3E%3Ccircle fill=&#39;hsl(128, 8%, 22%)&#39; cx=&#39;32&#39; cy=&#39;32&#39; r=&#39;32&#39; /%3E%3Ctext x=&#39;50%25&#39; y=&#39;55%25&#39; dominant-baseline=&#39;middle&#39; text-anchor=&#39;middle&#39; fill=&#39;%23FFF&#39; %3EA%3C/text%3E%3C/svg%3E"
		/>
	
			  </div> <div class="has-line-height-reset has-text-wrap width-250">
			<span
						class="has-text-subtle font-weight-semibold padding-right-xxs font-size-sm"
				  >
						匿名
				  </span>
			
			
		</div>
				</div>
			</div>
			<div class="level-right margin-top-none">
				<span class="font-size-xs"> <local-time format="datetime" datetime="2019-08-30T05:31:08+00:00" class="is-visually-hidden">2019-08-30T05:31:08+00:00</local-time> </span>
			</div>
		
		</div>
		
		<div>
			 <div class="content" itemprop="text"><p>那可以再试一下覆盖安装的方式更新一下系统,该操作不会影响到您的个人资料及已安装应用: </p>
<p>可以参考我的这篇文章中有详细的操作步骤: </p>
<p><a>http://alian.fun/archives/239</a> </p>
<p>_______________________________________________ </p>
<p>免责声明:以上涉及到的网址链接均[不是]来自微软的官方网站.这些页面所提供的信息均较为准确可靠.不过依然提醒您需要注意这些页面可能会包含您有可能不需要的产品广告,所以在您下载安装这些广告产品的时候请务必看清楚并确认这是您所需要的.</p>
</div> 
		</div>
		<div>
			
			<div
		class="margin-block-xxs padding-xs background-color-body-medium font-size-sm display-flex flex-direction-column border-radius-lg"
	>
		<div class="display-flex justify-content-space-between flex-wrap-wrap align-items-baseline">
			<div class="display-flex flex-grow-1">
				 <form
		method="POST"
		id="answer-rating-form-5042886"
		data-answer-id="5042886"
		new
		novalidate
		class="width-full"
	>
		<form-behavior header-X-DocsAuth="cookie" new></form-behavior>
		<div
			class="display-flex justify-content-space-between align-items-center flex-wrap-wrap"
		>
			
						<div
							class="field display-inline-flex flex-wrap-wrap align-items-center margin-bottom-none"
						>
							<label
								class="field-label margin-bottom-none margin-right-xs"
								for="answer-helpful-rating-5042886"
								>
				<a
					class="not-authenticated link-button font-size-sm margin-right-sm docs-sign-in"
					data-bi-name="answer-rating-sign-in"
					href="#"
				>
					请登录以评价此答案。
				</a>
		  
							</label>
							<div
								id="answer-helpful-rating-5042886"
								class="field-body flex-grow-0 margin-top-none"
							>
								
		<button
			id="answer-rating-5042886-yes"
			class="authenticated thumb-rating button button-primary button-sm  like"
			type="submit"
			form="answer-rating-form-5042886"
			formaction="/api/questions/answers/5042886/helpful/yes?_method=PUT&amp;locale=zh-cn"
			data-action=/api/questions/answers/5042886/accept?_method=PUT&amp;locale=zh-cn
			data-answer-rating-button
			data-bi-name="answer-rating-yes"
			title="此答案很有用"
			
		>
			<span aria-hidden="true" class="icon docon docon-like"></span>
			<span>是</span>
		</button>
	
								
		<button
			id="answer-rating-5042886-no"
			class="authenticated thumb-rating button button-primary button-sm  dislike"
			type="submit"
			form="answer-rating-form-5042886"
			formaction="/api/questions/answers/5042886/helpful/no?_method=PUT&amp;locale=zh-cn"
			
			data-answer-rating-button
			data-bi-name="answer-rating-no"
			title="此答案没有用处"
			
		>
			<span aria-hidden="true" class="icon docon docon-dislike"></span>
			<span>否</span>
		</button>
	
							</div>
						</div>
				  
			<span class="margin-top-xxs margin-top-none-tablet" id="answer-helpful-text-5042886"
				></span
			>
		</div>
	</form>
			</div>
		</div>
		
	</div>
			<div class="buttons margin-block-xxs margin-top-xs">
				
				<span
					class="button button-clear text-decoration-none has-inner-focus margin-bottom-none font-size-md color-text-subtle align-self-flex-start"
					title="无注释"
				>
					<span class="icon" aria-hidden="true">
						<span class="docon docon-comment-outline"></span>
					</span>
					<span class="font-size-sm is-visually-hidden-mobile">0 个注释</span>
					<span class="visually-hidden">无注释</span>
				</span>
		  
				
		
		
		<button
			title="报告问题"
			class="button button-clear text-decoration-none has-inner-focus margin-bottom-none button-primary qna-report-post-button  "
			data-list-type="report"
			data-bi-name="report"
			data-reportedUserId="a4d008df-4ece-4cb3-b7c9-d1be5ac4cacf"
			data-questionId="3209892"
			data-entityId="5042886"
			data-entityType="answer"
			data-action="/api/questions/moderation/reportconcern?_method=PUT&amp;locale=zh-cn"
			data-page-action-item="overflow-mobile"
			data-test-id="report-post-button-answer-5042886"
		>
			<span class="icon " aria-hidden="true">
				<span class="docon docon-flag"></span>
			</span>
			<span class="font-size-sm is-visually-hidden-mobile"
				>报告问题</span
			>
		</button>
	
		
		
	
			</div>
			
		<div id="question-answer4-5042886-expander" class="width-full">
			<div class="expandable ">
				<div id="question-answer4-5042886-moreThan">
					
		<ol>
			
		</ol>
	
				</div>
				
			</div>
		</div>
	
			
		</div>
	</div>
				</div>
			</li>
		 
		</ol>
	
					 <nav
		class="pagination is-centered margin-top-lg margin-bottom-xs"
		aria-label="分页"
		data-test-id="pagination-list"
	>
		<ul class="pagination-list">
			<li>
		<a
			type="button"
			data-test-id="pagination-button-back"
			class="pagination-link display-none"
			aria-label="上一篇"
			href="/zh-cn/answers/questions/3209892/windows10-64?forum=windows-all&amp;referrer=answers&amp;page=0#answers"
			aria-hidden="true"
		>
			<span class="icon" aria-hidden="true">
				<span class="docon docon-arrow-left"></span>
			</span>
		</a>
	</li>
			<li>
		<a
			type="button"
			class="is-hidden"
			data-page="1"
			aria-label="第 1 页，共 2 页"
			aria-current="false"
			3
			href="/zh-cn/answers/questions/3209892/windows10-64?forum=windows-all&amp;referrer=answers&amp;page=1#answers"
		>
			1
		</a>
	</li>
			<li class="is-hidden">
				<span>...</span>
			</li>
			
			<li>
				<a
					data-test-id="pagination-button-1"
					type="button"
					class="pagination-link is-current"
					data-page="1"
					aria-label="第 1 页，共 2 页"
					aria-current="true"
					href="/zh-cn/answers/questions/3209892/windows10-64?forum=windows-all&amp;referrer=answers&amp;page=1#answers"
				>
					1
				</a>
			</li>
		
			<li>
				<a
					data-test-id="pagination-button-2"
					type="button"
					class="pagination-link "
					data-page="2"
					aria-label="第 2 页，共 2 页"
					aria-current="false"
					href="/zh-cn/answers/questions/3209892/windows10-64?forum=windows-all&amp;referrer=answers&amp;page=2#answers"
				>
					2
				</a>
			</li>
		
			<li class="is-hidden">
				<span>...</span>
			</li>
			<li>
		<a
			type="button"
			class="pagination-link is-hidden"
			data-page="2"
			aria-label="第 2 页，共 2 页"
			aria-current="false"
			href="/zh-cn/answers/questions/3209892/windows10-64?forum=windows-all&amp;referrer=answers&amp;page=2#answers"
		>
			2
		</a>
	</li>
			<li> <a
		type="button"
		data-test-id="pagination-button-forward"
		class="pagination-next "
		aria-label="下一步"
		href="/zh-cn/answers/questions/3209892/windows10-64?forum=windows-all&amp;referrer=answers&amp;page=2#answers"
		aria-hidden="false"
	>
		<span class="icon" aria-hidden="true">
			<span class="docon docon-arrow-right"> </span>
		</span>
	</a></li>
		</ul>
	</nav>
					
				</div>
			</div>
			<aside
				id="ms--additional-resources-mobile"
				aria-label="其他资源"
				class="display-none-tablet display-none-print"
				data-test-id="additional-resources-mobile"
			>
				<hr class="hr" hidden />
				<h2 id="ms--additional-resources-mobile-heading" class="title is-3" hidden>
					其他资源
				</h2>
				<section
					id="right-rail-recommendations-mobile"
					data-bi-name="recommendations"
					data-test-id="right-rail-recommendations-mobile"
					hidden
				></section>
				<section
					id="right-rail-training-mobile"
					data-bi-name="learning-resources-card"
					hidden
				></section>
				<section id="right-rail-events-mobile" data-bi-name="events-card" hidden></section>
			</aside>
		</div>
	
				</main>
				<aside
					id="layout-body-aside"
					class="layout-body-aside "
					data-bi-name="aside"
			  >
					
			  </aside>  <div class="layout-body-footer " data-bi-name="layout-footer">
		<footer
			id="footer"
			data-test-id="footer"
			data-bi-name="footer"
			class="footer-layout has-padding has-default-focus border-top  uhf-container"
			role="contentinfo"
		>
			<div class="display-flex gap-xs flex-wrap-wrap is-full-height padding-right-lg-desktop">
				
		<a
			data-mscc-ic="false"
			href="#"
			data-bi-name="select-locale"
			class="locale-selector-link flex-shrink-0 button button-sm button-clear external-link-indicator"
			id=""
			title=""
			><span class="icon" aria-hidden="true"
				><span class="docon docon-world"></span></span
			><span class="local-selector-link-text">zh-cn</span></a
		>
	
				<div class="ccpa-privacy-link" data-ccpa-privacy-link hidden>
		
		<a
			data-mscc-ic="false"
			href="https://aka.ms/yourcaliforniaprivacychoices"
			data-bi-name="your-privacy-choices"
			class="button button-sm button-clear flex-shrink-0 external-link-indicator"
			id=""
			title=""
			>
		<svg
			xmlns="http://www.w3.org/2000/svg"
			viewBox="0 0 30 14"
			xml:space="preserve"
			height="16"
			width="43"
			aria-hidden="true"
			focusable="false"
		>
			<path
				d="M7.4 12.8h6.8l3.1-11.6H7.4C4.2 1.2 1.6 3.8 1.6 7s2.6 5.8 5.8 5.8z"
				style="fill-rule:evenodd;clip-rule:evenodd;fill:#fff"
			></path>
			<path
				d="M22.6 0H7.4c-3.9 0-7 3.1-7 7s3.1 7 7 7h15.2c3.9 0 7-3.1 7-7s-3.2-7-7-7zm-21 7c0-3.2 2.6-5.8 5.8-5.8h9.9l-3.1 11.6H7.4c-3.2 0-5.8-2.6-5.8-5.8z"
				style="fill-rule:evenodd;clip-rule:evenodd;fill:#06f"
			></path>
			<path
				d="M24.6 4c.******* 0 .8L22.5 7l2.2 2.2c.******* 0 .8-.2.2-.6.2-.8 0l-2.2-2.2-2.2 2.2c-.2.2-.6.2-.8 0-.2-.2-.2-.6 0-.8L20.8 7l-2.2-2.2c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0l2.2 2.2L23.8 4c.2-.2.6-.2.8 0z"
				style="fill:#fff"
			></path>
			<path
				d="M12.7 4.1c.*******.1.8L8.6 9.8c-.1.1-.2.2-.3.2-.2.1-.5.1-.7-.1L5.4 7.7c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0L8 8.6l3.8-4.5c.2-.2.6-.2.9 0z"
				style="fill:#06f"
			></path>
		</svg>
	
			<span>你的隐私选择</span></a
		>
	
	</div>
				<div class="flex-shrink-0">
		<div class="dropdown has-caret-up">
			<button
				data-test-id="theme-selector-button"
				class="dropdown-trigger button button-clear button-sm has-inner-focus theme-dropdown-trigger"
				aria-controls="{{ themeMenuId }}"
				aria-expanded="false"
				title="主题"
				data-bi-name="theme"
			>
				<span class="icon">
					<span class="docon docon-sun" aria-hidden="true"></span>
				</span>
				<span>主题</span>
				<span class="icon expanded-indicator" aria-hidden="true">
					<span class="docon docon-chevron-down-light"></span>
				</span>
			</button>
			<div class="dropdown-menu" id="{{ themeMenuId }}" role="menu">
				<ul class="theme-selector padding-xxs" data-test-id="theme-dropdown-menu">
					<li class="theme display-block">
						<button
							class="button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left"
							data-theme-to="light"
						>
							<span class="theme-light margin-right-xxs">
								<span
									class="theme-selector-icon border display-inline-block has-body-background"
									aria-hidden="true"
								>
									<svg class="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 14">
										<rect width="22" height="14" class="has-fill-body-background" />
										<rect x="5" y="5" width="12" height="4" class="has-fill-secondary" />
										<rect x="5" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="8" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="11" y="2" width="3" height="1" class="has-fill-secondary" />
										<rect x="1" y="1" width="2" height="2" class="has-fill-secondary" />
										<rect x="5" y="10" width="7" height="2" rx="0.3" class="has-fill-primary" />
										<rect x="19" y="1" width="2" height="2" rx="1" class="has-fill-secondary" />
									</svg>
								</span>
							</span>
							<span role="menuitem"> 亮 </span>
						</button>
					</li>
					<li class="theme display-block">
						<button
							class="button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left"
							data-theme-to="dark"
						>
							<span class="theme-dark margin-right-xxs">
								<span
									class="border theme-selector-icon display-inline-block has-body-background"
									aria-hidden="true"
								>
									<svg class="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 14">
										<rect width="22" height="14" class="has-fill-body-background" />
										<rect x="5" y="5" width="12" height="4" class="has-fill-secondary" />
										<rect x="5" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="8" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="11" y="2" width="3" height="1" class="has-fill-secondary" />
										<rect x="1" y="1" width="2" height="2" class="has-fill-secondary" />
										<rect x="5" y="10" width="7" height="2" rx="0.3" class="has-fill-primary" />
										<rect x="19" y="1" width="2" height="2" rx="1" class="has-fill-secondary" />
									</svg>
								</span>
							</span>
							<span role="menuitem"> 暗 </span>
						</button>
					</li>
					<li class="theme display-block">
						<button
							class="button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left"
							data-theme-to="high-contrast"
						>
							<span class="theme-high-contrast margin-right-xxs">
								<span
									class="border theme-selector-icon display-inline-block has-body-background"
									aria-hidden="true"
								>
									<svg class="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 14">
										<rect width="22" height="14" class="has-fill-body-background" />
										<rect x="5" y="5" width="12" height="4" class="has-fill-secondary" />
										<rect x="5" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="8" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="11" y="2" width="3" height="1" class="has-fill-secondary" />
										<rect x="1" y="1" width="2" height="2" class="has-fill-secondary" />
										<rect x="5" y="10" width="7" height="2" rx="0.3" class="has-fill-primary" />
										<rect x="19" y="1" width="2" height="2" rx="1" class="has-fill-secondary" />
									</svg>
								</span>
							</span>
							<span role="menuitem"> 高对比度 </span>
						</button>
					</li>
				</ul>
			</div>
		</div>
	</div>
			</div>
			<ul class="links" data-bi-name="footerlinks">
				<li class="manage-cookies-holder" hidden=""></li>
				<li>
		
		<a
			data-mscc-ic="false"
			href="https://learn.microsoft.com/zh-cn/principles-for-ai-generated-content"
			data-bi-name="aiDisclaimer"
			class=" external-link-indicator"
			id=""
			title=""
			>AI Disclaimer</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://learn.microsoft.com/zh-cn/previous-versions/"
			data-bi-name="archivelink"
			class=" external-link-indicator"
			id=""
			title=""
			>早期版本</a
		>
	
	</li> <li>
		
		<a
			data-mscc-ic="false"
			href="https://techcommunity.microsoft.com/t5/microsoft-learn-blog/bg-p/MicrosoftLearnBlog"
			data-bi-name="bloglink"
			class=" external-link-indicator"
			id=""
			title=""
			>博客</a
		>
	
	</li> <li>
		
		<a
			data-mscc-ic="false"
			href="https://learn.microsoft.com/zh-cn/contribute"
			data-bi-name="contributorGuide"
			class=" external-link-indicator"
			id=""
			title=""
			>参与</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://go.microsoft.com/fwlink/?LinkId=521839"
			data-bi-name="privacy"
			class=" external-link-indicator"
			id=""
			title=""
			>隐私</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://learn.microsoft.com/zh-cn/legal/termsofuse"
			data-bi-name="termsofuse"
			class=" external-link-indicator"
			id=""
			title=""
			>使用条款</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://aka.ms/msftqacodeconduct"
			data-bi-name="codeofconduct"
			class=" external-link-indicator"
			id=""
			title=""
			>行为准则</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://www.microsoft.com/legal/intellectualproperty/Trademarks/"
			data-bi-name="trademarks"
			class=" external-link-indicator"
			id=""
			title=""
			>商标</a
		>
	
	</li>
				<li>&copy; Microsoft 2025</li>
			</ul>
		</footer>
	</footer>
			</body>
		</html>