from math import radians, sin, cos, sqrt, atan2

def haversine(lat1, lon1, lat2, lon2):
    # 地球半径（公里）
    R = 6371
    # 转换为弧度
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    # 纬度和经度差
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    # Haversine公式
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    distance_km = R * c
    distance_miles = distance_km * 0.621371  # 转换为英里
    return distance_km, distance_miles

# 输入坐标
lat1, lon1 = 34.9752723, -80.5321799
lat2, lon2 = 40.1364991, -74.7287714


# 计算距离
distance_km, distance_miles = haversine(lat1, lon1, lat2, lon2)

# 输出结果
print(f"距离：{distance_km:.2f} 公里")
print(f"距离：{distance_miles:.2f} 英里")